"""
План<PERSON><PERSON>овщик задач для Telegram-бота погоды.

Инициализация JobQueue, планирование задач обновления.
Определяет функции для регистрации периодических задач
для каждого типа сообщения с различными интервалами:
- Прогноз на 3 дня: каждые 12 часов
- Прогноз на сегодня: ежечасно
- Погода сейчас: каждые 10 минут

Этап 9 плана разработки.
"""

import logging
from typing import Optional
from telegram.ext import Application

# Импорт функций обновления из weather_handlers
from weather_handlers import update_3days, update_today, update_current

# Импорт констант из конфигурации
from weather_config import (
    UPDATE_INTERVAL_3DAYS,
    UPDATE_INTERVAL_TODAY,
    UPDATE_INTERVAL_CURRENT,
    CHANNEL_ID
)

# Импорт хранилища для получения message_id
from weather_storage import Storage

# Настройка логирования
logger = logging.getLogger(__name__)


def schedule_updates(application: Application, storage: Storage) -> bool:
    """
    Планирует периодические задачи обновления погодных сообщений.

    Регистрирует три повторяющиеся задачи через job_queue.run_repeating():
    - update_3days с интервалом UPDATE_INTERVAL_3DAYS (12 часов)
    - update_today с интервалом UPDATE_INTERVAL_TODAY (1 час)
    - update_current с интервалом UPDATE_INTERVAL_CURRENT (10 минут)

    Args:
        application: Объект Application из python-telegram-bot
        storage: Объект Storage для доступа к сохраненным message_id

    Returns:
        bool: True если планировщик успешно запущен, False иначе
    """
    try:
        # Получаем job_queue из application
        job_queue = application.job_queue

        if not job_queue:
            logger.error("JobQueue не доступен в application")
            return False

        # Загружаем данные из хранилища
        data = storage.load()

        # Проверяем наличие сохраненных message_id
        if not data or "messages" not in data:
            logger.warning("Нет сохраненных message_id. Планировщик не запущен.")
            return False

        messages_data = data["messages"]
        logger.info(f"Загружены данные сообщений: {messages_data}")

        # Проверяем наличие всех необходимых типов сообщений
        required_types = ['3days', 'today', 'current']
        missing_types = []

        for msg_type in required_types:
            if (msg_type not in messages_data or
                not messages_data[msg_type] or
                'message_id' not in messages_data[msg_type] or
                messages_data[msg_type]['message_id'] is None):
                missing_types.append(msg_type)

        if missing_types:
            logger.warning(f"Отсутствуют message_id для типов: {missing_types}")
            logger.warning("Планировщик запущен частично")

        # Счетчик успешно запланированных задач
        scheduled_count = 0

        # Планируем задачу обновления прогноза на 3 дня
        if ('3days' in messages_data and
            messages_data['3days'] and
            messages_data['3days'].get('message_id')):

            message_id = messages_data['3days']['message_id']
            logger.info(f"📅 Планирование обновления прогноза на 3 дня для message_id: {message_id}")

            job_queue.run_repeating(
                callback=update_3days,
                interval=UPDATE_INTERVAL_3DAYS,
                first=10,  # Запуск через 10 секунд после регистрации
                name='update_3days',
                data={
                    'chat_id': data.get('channel_id', CHANNEL_ID),
                    'type': '3days',
                    'message_id': message_id
                }
            )
            scheduled_count += 1
            logger.info(f"✅ Запланировано обновление прогноза на 3 дня каждые {UPDATE_INTERVAL_3DAYS} сек")

        # Планируем задачу обновления прогноза на сегодня
        if ('today' in messages_data and
            messages_data['today'] and
            messages_data['today'].get('message_id')):

            message_id = messages_data['today']['message_id']
            logger.info(f"📅 Планирование обновления прогноза на сегодня для message_id: {message_id}")

            job_queue.run_repeating(
                callback=update_today,
                interval=UPDATE_INTERVAL_TODAY,
                first=15,  # Запуск через 15 секунд после регистрации
                name='update_today',
                data={
                    'chat_id': data.get('channel_id', CHANNEL_ID),
                    'type': 'today',
                    'message_id': message_id
                }
            )
            scheduled_count += 1
            logger.info(f"✅ Запланировано обновление прогноза на сегодня каждые {UPDATE_INTERVAL_TODAY} сек")

        # Планируем задачу обновления текущей погоды
        if ('current' in messages_data and
            messages_data['current'] and
            messages_data['current'].get('message_id')):

            message_id = messages_data['current']['message_id']
            logger.info(f"📅 Планирование обновления текущей погоды для message_id: {message_id}")

            job_queue.run_repeating(
                callback=update_current,
                interval=UPDATE_INTERVAL_CURRENT,
                first=20,  # Запуск через 20 секунд после регистрации
                name='update_current',
                data={
                    'chat_id': data.get('channel_id', CHANNEL_ID),
                    'type': 'current',
                    'message_id': message_id
                }
            )
            scheduled_count += 1
            logger.info(f"✅ Запланировано обновление текущей погоды каждые {UPDATE_INTERVAL_CURRENT} сек")

        if scheduled_count > 0:
            logger.info(f"🚀 Планировщик запущен успешно. Активных задач: {scheduled_count}")
            return True
        else:
            logger.error("❌ Не удалось запланировать ни одной задачи")
            return False

    except Exception as e:
        logger.error(f"Ошибка при запуске планировщика: {e}")
        return False


def stop_all_jobs(application: Application) -> bool:
    """
    Останавливает все запланированные задачи.

    Args:
        application: Объект Application из python-telegram-bot

    Returns:
        bool: True если задачи успешно остановлены, False иначе
    """
    try:
        job_queue = application.job_queue

        if not job_queue:
            logger.warning("JobQueue не доступен")
            return False

        # Получаем все активные задачи
        jobs = job_queue.jobs()

        if not jobs:
            logger.info("Нет активных задач для остановки")
            return True

        # Останавливаем все задачи
        stopped_count = 0
        for job in jobs:
            if job.name and job.name.startswith('update_'):
                job.schedule_removal()
                stopped_count += 1
                logger.info(f"🛑 Остановлена задача: {job.name}")

        logger.info(f"✅ Остановлено задач: {stopped_count}")
        return True

    except Exception as e:
        logger.error(f"Ошибка при остановке задач: {e}")
        return False


def get_job_status(application: Application) -> dict:
    """
    Получает статус всех запланированных задач.

    Args:
        application: Объект Application из python-telegram-bot

    Returns:
        dict: Словарь со статусом задач
    """
    try:
        job_queue = application.job_queue

        if not job_queue:
            return {"error": "JobQueue не доступен"}

        jobs = job_queue.jobs()

        status = {
            "total_jobs": len(jobs),
            "weather_jobs": [],
            "other_jobs": []
        }

        for job in jobs:
            job_info = {
                "name": job.name or "unnamed",
                "enabled": job.enabled,
                "interval": getattr(job, 'interval', None),
                "next_run": str(job.next_t) if hasattr(job, 'next_t') else None
            }

            if job.name and job.name.startswith('update_'):
                status["weather_jobs"].append(job_info)
            else:
                status["other_jobs"].append(job_info)

        return status

    except Exception as e:
        logger.error(f"Ошибка при получении статуса задач: {e}")
        return {"error": str(e)}


def restart_scheduler(application: Application, storage: Storage) -> bool:
    """
    Перезапускает планировщик: останавливает все задачи и запускает заново.

    Args:
        application: Объект Application из python-telegram-bot
        storage: Объект Storage для доступа к сохраненным message_id

    Returns:
        bool: True если перезапуск успешен, False иначе
    """
    logger.info("🔄 Перезапуск планировщика...")

    # Останавливаем все задачи
    if not stop_all_jobs(application):
        logger.error("Не удалось остановить задачи")
        return False

    # Запускаем планировщик заново
    if schedule_updates(application, storage):
        logger.info("✅ Планировщик успешно перезапущен")
        return True
    else:
        logger.error("❌ Не удалось перезапустить планировщик")
        return False
