#!/usr/bin/env python3
"""
Тестовый скрипт для проверки новых форматов сообщений о погоде
"""

from weather_utils import (
    create_weather_caption_current,
    create_weather_caption_today,
    create_weather_caption_3days,
    describe_wind_strength,
    describe_humidity_level
)

def test_wind_descriptions():
    """Тестируем описания ветра"""
    print("=== Тест описаний ветра ===")
    wind_speeds = [0, 1, 3, 5, 8, 11, 15, 20, 25, 30]
    for speed in wind_speeds:
        desc = describe_wind_strength(speed)
        print(f"Ветер {speed} м/с: {desc}")
    print()

def test_humidity_descriptions():
    """Тестируем описания влажности"""
    print("=== Тест описаний влажности ===")
    humidity_levels = [20, 35, 50, 65, 75, 85]
    for humidity in humidity_levels:
        desc = describe_humidity_level(humidity)
        print(f"Влажность {humidity}%: {desc}")
    print()

def test_current_weather():
    """Тестируем формат текущей погоды"""
    print("=== Тест текущей погоды ===")
    current_data = {
        'temp': 24,
        'feels_like': 26,
        'description': 'Солнечно',
        'wind_speed': 7.4,
        'humidity': 64,
        'is_day': True
    }
    
    caption = create_weather_caption_current(current_data)
    print(caption)
    print()

def test_today_weather():
    """Тестируем формат погоды на сегодня"""
    print("=== Тест погоды на сегодня ===")
    today_data = {
        'periods': {
            'morning': {
                'name': 'Утро',
                'temp': 23,
                'feels_like': 25,
                'description': 'Солнечно',
                'wind_speed': 6.4,
                'is_day': True
            },
            'day': {
                'name': 'День',
                'temp': 26,
                'feels_like': 26,
                'description': 'Солнечно',
                'wind_speed': 8.4,
                'is_day': True
            },
            'evening': {
                'name': 'Вечер',
                'temp': 24,
                'feels_like': 26,
                'description': 'Ясно',
                'wind_speed': 6.1,
                'is_day': False
            },
            'night': {
                'name': 'Ночь',
                'temp': 22,
                'feels_like': 25,
                'description': 'Ясно',
                'wind_speed': 6.2,
                'is_day': False
            }
        },
        'sunrise_display': 'Восход: 05:32',
        'sunset_formatted': '20:12'
    }
    
    caption = create_weather_caption_today(today_data)
    print(caption)
    print()

def test_3days_weather():
    """Тестируем формат прогноза на 3 дня"""
    print("=== Тест прогноза на 3 дня ===")
    forecast_data = [
        {
            'date': 'Чт, 31 июля',
            'temp_min': 22,
            'temp_max': 26,
            'description': 'Солнечно',
            'wind_speed': 8.5
        },
        {
            'date': 'Пт, 1 августа',
            'temp_min': 21,
            'temp_max': 27,
            'description': 'Солнечно',
            'wind_speed': 7.8
        },
        {
            'date': 'Сб, 2 августа',
            'temp_min': 23,
            'temp_max': 30,
            'description': 'Солнечно',
            'wind_speed': 7.0
        }
    ]
    
    caption = create_weather_caption_3days(forecast_data)
    print(caption)
    print()

if __name__ == "__main__":
    test_wind_descriptions()
    test_humidity_descriptions()
    test_current_weather()
    test_today_weather()
    test_3days_weather()
