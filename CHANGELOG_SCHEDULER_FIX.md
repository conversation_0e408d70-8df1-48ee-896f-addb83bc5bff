# Исправления планировщика для корректной работы после перезапуска

## Проблема
После перезапуска бота планировщик не мог найти сохраненные `message_id` и не запускал задачи обновления постов погоды. Команда "weather" писалась в канал каждый раз заново.

## Причина
В файле `weather_scheduler.py` была ошибка в логике проверки данных из хранилища. Код проверял наличие типов сообщений в корневом объекте `data`, но они находятся в `data["messages"]`.

## Исправления

### 1. weather_scheduler.py
- **Исправлена логика проверки данных** (строки 60-80):
  - Добавлена проверка наличия ключа "messages" в данных
  - Исправлена проверка типов сообщений: `msg_type in data["messages"]` вместо `msg_type in data`
  - Добавлено логирование загруженных данных для отладки

- **Исправлена логика планирования задач** (строки 89-153):
  - Правильное обращение к `messages_data[msg_type]["message_id"]`
  - Использование сохраненного `channel_id` из хранилища с fallback на конфигурацию
  - Изменены интервалы первого запуска (10, 15, 20 секунд) для избежания конфликтов
  - Добавлено детальное логирование для каждой планируемой задачи

### 2. weather_handlers.py
- **Улучшена обработка ошибок обновления** (строки 256-373):
  - Добавлен fallback на `CHANNEL_ID` из конфигурации если нет сохраненного
  - Улучшена обработка ошибок Telegram API
  - Автоматическая очистка `message_id` если сообщение было удалено
  - Более детальное логирование процесса обновления

### 3. weather_storage.py
- **Обновлен тип параметра** (строка 97):
  - `message_id: Optional[int]` вместо `message_id: int`
  - Теперь метод может принимать `None` для очистки message_id

### 4. weather_main.py
- **Улучшено логирование при запуске** (строки 91-114):
  - Подробная информация о найденных сохраненных сообщениях
  - Отображение сохраненного `channel_id`
  - Более информативные сообщения о статусе планировщика

## Результат
✅ **Планировщик теперь корректно работает после перезапуска бота**
✅ **Команда "weather" нужна только один раз для определения канала**
✅ **Посты автоматически обновляются согласно расписанию**
✅ **Улучшена обработка ошибок и логирование**

## Тестирование
Создан тестовый скрипт `test_scheduler_fix.py` для проверки корректности исправлений.

## Интервалы обновления
- **Прогноз на 3 дня**: каждые 12 часов (первый запуск через 10 сек)
- **Прогноз на сегодня**: каждый час (первый запуск через 15 сек)  
- **Текущая погода**: каждые 10 минут (первый запуск через 20 сек)

## Использование
1. Запустите бота: `python weather_main.py`
2. Если есть сохраненные сообщения - планировщик запустится автоматически
3. Если нет - отправьте "weather" в канал один раз
4. Посты будут обновляться автоматически по расписанию
